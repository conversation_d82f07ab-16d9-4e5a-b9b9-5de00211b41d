# 全局变量定义
# ————————————一般训练参数————————————
# 训练模型 可修改
model_file = "yolov8-lite-fruit.yaml"

# 表示训练/测试集 可修改
dataset = "fruit.yaml"

# 表示训练epoch
epochs = 2

# 表示是否在训练过程中保存
bool_save = True

# 表示是若保存则多少epochs保存一次
save_period = 50

# 表示是否在过去的基础上继续训练
bool_resume = False

# 训练在cpu上还是在gpu上
device = '0'

# 设置batch_size
batch_size = 16

# 测试图像
test_graph = "./(f)images/orange.jpg"

# 默认的模型大小
scale_default = 'n'

# 是否将pt格式导出为onnx和ncnn格式(正常情况应保证始终为0)
exchange = 1

# ————————————蒸馏训练参数————————————
# 表示是否蒸馏
bool_distill = True

# 定义新模型与旧模型的各个层的对应列表：
# teacher_peer_list表示教师模型与新模型的层的对应关系，总长度为23
teacher_peer_list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 45]

# student_peer_list表示学生模型与新模型的层的对应关系，总长度为23，总长度为10
# student_peer_list = [23, 24, 25, 26, 27, 28, 29, 30, 31, 33]  # 轻量化
student_peer_list = [23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46]  # 未轻量化

# training时输出的loss：1.教师训练loss；2.学生训练loss；3.蒸馏训练loss（异常情况则当作3处理）
flag_train_output = 3

# val时输出的预测结果是否为学生的预测结果，否则为教师的预测结果
bool_val_output = True

# 蒸馏模型尺寸 可修改
scale_student = 'n'
scale_teacher = 's'

# 蒸馏超参数设置 可修改
hyp_T = 0.05
hyp_box_distill = 0.70
hyp_cls_distill = 1.10
hyp_dfl_distill = 1.00
# 以下参数范围从0.0到0.1，值越高蒸馏比例越大
hyp_w_t_cls = 0.99
hyp_w_t_box = 0.99
hyp_w_t_dfl = 0.99

# 蒸馏训练完成后的学生模型
model_based_file = "./(f)models/model_based_file.pt"

# 预训练文件
path_student = './(f)models/yolov8n-fruit.pt'
path_teacher = './(f)models/yolov8s-fruit.pt'

# 预训练文件修改后的保存路径
path_student_prepare = './(f)models/yolov8_student_prepare-fruit.pth'
path_teacher_prepare = './(f)models/yolov8_teacher_prepare-fruit.pth'

# 蒸馏最终结果文件
final = './(f)models/final.pt'

# 表示蒸馏阶段
phase = 1
